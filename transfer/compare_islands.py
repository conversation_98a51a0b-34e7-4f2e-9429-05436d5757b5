# compare_islands.py
import json
import logging
from collections import defaultdict, deque
from datetime import datetime


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('../out/island_analysis.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)


class GridIslands:
    def __init__(self, nodes, edges, consider_status=True):
        if isinstance(nodes, list):
            self.nodes = {n["id"]: n for n in nodes}
        elif isinstance(nodes, dict):
            self.nodes = nodes
        else:
            raise ValueError("nodes must be a list or dict")

        self.edges = edges
        self.consider_status = consider_status
        self.graph = defaultdict(list)
        self.islands = []

        self.build_graph()

    def build_graph(self):
        """构建邻接表，同时记录断开的边"""
        self.disconnected_edges = []
        self.connected_edges = []
        total_edges = len(self.edges)

        logger.info(f"开始构建图，总边数: {total_edges}")

        for i, edge in enumerate(self.edges):
            f = edge["from"].split(" ")[0]
            t = edge["to"].split(" ")[0]

            # 检查节点是否存在
            if f not in self.nodes or t not in self.nodes:
                logger.warning(f"边 {i+1}/{total_edges}: 节点不存在 - {f} 或 {t}")
                continue

            if self.consider_status:
                devices_status = edge.get("devices_status", [])
                open_devices = [dev["name"] for dev in devices_status if dev.get("status") == "OPEN"]
                closed_devices = [dev["name"] for dev in devices_status if dev.get("status") == "CLOSED"]

                if open_devices:
                    # 记录断开的边
                    edge_info = {
                        "from": f,
                        "to": t,
                        "from_name": self.nodes[f].get("name", "未知"),
                        "to_name": self.nodes[t].get("name", "未知"),
                        "open_devices": open_devices,
                        "closed_devices": closed_devices,
                        "edge_index": i
                    }
                    self.disconnected_edges.append(edge_info)
                    logger.debug(f"断开边: {self.nodes[f].get('name')} -> {self.nodes[t].get('name')}, "
                               f"断开设备: {open_devices}")
                    continue
                else:
                    # 记录连通的边
                    self.connected_edges.append({
                        "from": f,
                        "to": t,
                        "from_name": self.nodes[f].get("name", "未知"),
                        "to_name": self.nodes[t].get("name", "未知"),
                        "devices": closed_devices,
                        "edge_index": i
                    })

            # 添加到邻接表
            self.graph[f].append(t)
            self.graph[t].append(f)

        logger.info(f"图构建完成 - 连通边: {len(self.connected_edges)}, 断开边: {len(self.disconnected_edges)}")

        if self.disconnected_edges:
            logger.info("断开的边详情:")
            for edge in self.disconnected_edges[:10]:  # 只显示前10条
                logger.info(f"  {edge['from_name']} <-> {edge['to_name']}: {edge['open_devices']}")
            if len(self.disconnected_edges) > 10:
                logger.info(f"  ... 还有 {len(self.disconnected_edges) - 10} 条断开的边")

    def find_islands(self):
        """使用BFS算法查找连通分量（岛屿）"""
        visited = set()
        islands = []
        total_nodes = len(self.nodes)

        logger.info(f"开始岛屿分析，总节点数: {total_nodes}")

        for node_id in self.nodes:
            if node_id in visited:
                continue

            # BFS查找连通分量
            island_nodes = []
            queue = deque([node_id])
            visited.add(node_id)

            while queue:
                current = queue.popleft()
                island_nodes.append(current)
                for neighbor in self.graph[current]:
                    if neighbor not in visited:
                        visited.add(neighbor)
                        queue.append(neighbor)

            islands.append(island_nodes)

            # 记录岛屿信息
            island_idx = len(islands)
            logger.debug(f"发现岛屿 {island_idx}: {len(island_nodes)} 个节点")

        # 按节点数量排序（从大到小）
        islands.sort(key=len, reverse=True)
        self.islands = islands

        logger.info(f"岛屿分析完成，共发现 {len(islands)} 个岛屿")

        # 统计岛屿规模分布
        size_distribution = {}
        for island in islands:
            size = len(island)
            size_distribution[size] = size_distribution.get(size, 0) + 1

        logger.info("岛屿规模分布:")
        for size in sorted(size_distribution.keys(), reverse=True):
            count = size_distribution[size]
            logger.info(f"  {size} 个节点的岛屿: {count} 个")

        return islands

    def get_island_map(self):
        island_map = {}
        for idx, isl in enumerate(self.islands, 1):
            for node_id in isl:
                island_map[node_id] = idx
        return island_map

    def print_islands_summary(self, max_nodes_to_show=5):
        """打印岛屿摘要信息"""
        islands = self.islands
        logger.info(f"岛屿摘要 - 总数: {len(islands)}")

        for idx, isl in enumerate(islands, 1):
            node_count = len(isl)
            logger.info(f"岛屿 {idx}: {node_count} 个节点")

            # 显示部分节点信息
            nodes_to_show = min(max_nodes_to_show, len(isl))
            for i, nid in enumerate(isl[:nodes_to_show]):
                node_name = self.nodes[nid].get("name", "未知节点")
                node_type = self.nodes[nid].get("type", "未知类型")
                voltage = self.nodes[nid].get("voltage", "未知电压")
                logger.info(f"  - {nid}: {node_name} ({node_type}, {voltage})")

            if len(isl) > max_nodes_to_show:
                logger.info(f"  ... 还有 {len(isl) - max_nodes_to_show} 个节点")

    def save_islands_to_file(self, filepath):
        """保存岛屿信息到文件"""
        logger.info(f"保存岛屿信息到: {filepath}")

        islands_data = []
        total_nodes = 0

        for idx, isl in enumerate(self.islands, 1):
            nodes_info = []
            voltage_stats = {}
            type_stats = {}

            for nid in isl:
                node = self.nodes[nid]
                node_name = node.get("name", "未知节点")
                node_type = node.get("type", "未知类型")
                voltage = node.get("voltage", "未知电压")
                substation = node.get("substation", "未知厂站")

                nodes_info.append({
                    "id": nid,
                    "name": node_name,
                    "type": node_type,
                    "voltage": voltage,
                    "substation": substation
                })

                # 统计电压等级和节点类型
                voltage_stats[voltage] = voltage_stats.get(voltage, 0) + 1
                type_stats[node_type] = type_stats.get(node_type, 0) + 1

            total_nodes += len(isl)

            island_info = {
                "island_index": idx,
                "node_count": len(isl),
                "voltage_distribution": voltage_stats,
                "type_distribution": type_stats,
                "nodes": nodes_info
            }
            islands_data.append(island_info)

        # 添加总体统计信息
        summary = {
            "analysis_time": datetime.now().isoformat(),
            "total_islands": len(self.islands),
            "total_nodes": total_nodes,
            "consider_status": self.consider_status,
            "disconnected_edges_count": len(getattr(self, 'disconnected_edges', [])),
            "connected_edges_count": len(getattr(self, 'connected_edges', []))
        }

        output_data = {
            "summary": summary,
            "islands": islands_data
        }

        with open(filepath, "w", encoding="utf-8") as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)

        logger.info(f"岛划分结果已保存 - {len(islands_data)} 个岛屿，{total_nodes} 个节点")


from collections import defaultdict


def analyze_island_changes(grid_original, grid_maint):
    """详细分析岛屿变化"""
    logger.info("开始分析岛屿变化...")

    original_island_map = grid_original.get_island_map()
    maintenance_island_map = grid_maint.get_island_map()

    # 分析各种变化类型
    island_changes = []
    split_count = 0
    merge_count = 0
    unchanged_count = 0

    # 1. 分析岛屿拆分
    for orig_idx, orig_nodes in enumerate(grid_original.islands, 1):
        # 找到这些节点在检修后的分布
        new_distribution = {}
        missing_nodes = []

        for nid in orig_nodes:
            if nid in maintenance_island_map:
                new_idx = maintenance_island_map[nid]
                if new_idx not in new_distribution:
                    new_distribution[new_idx] = []
                new_distribution[new_idx].append(nid)
            else:
                missing_nodes.append(nid)

        if missing_nodes:
            logger.warning(f"原岛 {orig_idx} 中有 {len(missing_nodes)} 个节点在检修后消失")

        # 如果拆分成多个岛
        if len(new_distribution) > 1:
            split_count += 1

            # 找到导致拆分的断开边
            split_edges = []
            for e in grid_maint.disconnected_edges:
                from_island = maintenance_island_map.get(e["from"])
                to_island = maintenance_island_map.get(e["to"])
                if (from_island in new_distribution and
                    to_island in new_distribution and
                    from_island != to_island):
                    split_edges.append(e)

            # 计算拆分详情
            split_details = []
            for new_idx, nodes in new_distribution.items():
                node_names = [grid_maint.nodes[nid].get("name", "未知") for nid in nodes]
                voltage_dist = {}
                type_dist = {}

                for nid in nodes:
                    node = grid_maint.nodes[nid]
                    voltage = node.get("voltage", "未知")
                    node_type = node.get("type", "未知")
                    voltage_dist[voltage] = voltage_dist.get(voltage, 0) + 1
                    type_dist[node_type] = type_dist.get(node_type, 0) + 1

                split_details.append({
                    "new_island_index": new_idx,
                    "node_count": len(nodes),
                    "node_ids": nodes,
                    "sample_nodes": node_names[:3],  # 显示前3个节点名称
                    "voltage_distribution": voltage_dist,
                    "type_distribution": type_dist
                })

            change_info = {
                "type": "split",
                "original_island": orig_idx,
                "original_node_count": len(orig_nodes),
                "new_islands_count": len(new_distribution),
                "split_details": split_details,
                "split_edges": split_edges,
                "impact_score": len(orig_nodes) * len(new_distribution)  # 影响评分
            }
            island_changes.append(change_info)

            # 详细日志
            logger.info(f"🔥 岛屿拆分 - 原岛 {orig_idx} ({len(orig_nodes)} 节点) -> {len(new_distribution)} 个新岛")
            for detail in split_details:
                logger.info(f"  新岛 {detail['new_island_index']}: {detail['node_count']} 节点, "
                          f"样例: {', '.join(detail['sample_nodes'])}")

            if split_edges:
                logger.info(f"  导致拆分的断开边 ({len(split_edges)} 条):")
                for edge in split_edges[:3]:  # 只显示前3条
                    logger.info(f"    {edge['from_name']} <-> {edge['to_name']}: {edge['open_devices']}")
                if len(split_edges) > 3:
                    logger.info(f"    ... 还有 {len(split_edges) - 3} 条断开边")

        elif len(new_distribution) == 1:
            unchanged_count += 1

    # 2. 分析岛屿合并（检查是否有多个原岛合并成一个新岛）
    maint_to_orig_map = {}
    for orig_idx, orig_nodes in enumerate(grid_original.islands, 1):
        for nid in orig_nodes:
            if nid in maintenance_island_map:
                maint_idx = maintenance_island_map[nid]
                if maint_idx not in maint_to_orig_map:
                    maint_to_orig_map[maint_idx] = set()
                maint_to_orig_map[maint_idx].add(orig_idx)

    for maint_idx, orig_islands in maint_to_orig_map.items():
        if len(orig_islands) > 1:
            merge_count += 1
            logger.info(f"🔗 岛屿合并 - 原岛 {list(orig_islands)} 合并为新岛 {maint_idx}")

    logger.info(f"岛屿变化统计:")
    logger.info(f"  拆分: {split_count} 个原岛")
    logger.info(f"  合并: {merge_count} 个新岛")
    logger.info(f"  不变: {unchanged_count} 个岛")

    return island_changes


def compare_islands(grid_original, grid_maint, maintenance_nodes, maintenance_edges,
                    output_original, output_maintenance, output_newislands):
    """比较检修前后的岛屿变化"""
    logger.info("="*60)
    logger.info("开始岛屿比较分析")
    logger.info("="*60)

    # 保存原始/检修岛结果
    logger.info("保存岛屿分析结果...")
    grid_original.save_islands_to_file(output_original)
    grid_maint.save_islands_to_file(output_maintenance)

    # 基本统计
    orig_count = len(grid_original.islands)
    maint_count = len(grid_maint.islands)
    island_diff = maint_count - orig_count

    logger.info(f"基本统计:")
    logger.info(f"  原始拓扑岛数量: {orig_count}")
    logger.info(f"  检修拓扑岛数量: {maint_count}")
    logger.info(f"  岛数量变化: {island_diff:+d}")

    # 详细分析岛屿变化
    island_changes = analyze_island_changes(grid_original, grid_maint)

    # 分析断开边的影响
    disconnected_count = len(grid_maint.disconnected_edges)
    if disconnected_count > 0:
        logger.info(f"断开边分析:")
        logger.info(f"  总断开边数: {disconnected_count}")

        # 按设备类型统计
        device_types = {}
        for edge in grid_maint.disconnected_edges:
            for device in edge['open_devices']:
                device_type = "刀闸" if "刀闸" in device else ("开关" if "开关" in device else "其他")
                device_types[device_type] = device_types.get(device_type, 0) + 1

        logger.info(f"  断开设备类型分布: {device_types}")

    # 保存详细变化信息
    change_summary = {
        "analysis_time": datetime.now().isoformat(),
        "original_islands_count": orig_count,
        "maintenance_islands_count": maint_count,
        "island_count_change": island_diff,
        "disconnected_edges_count": disconnected_count,
        "changes": island_changes,
        "statistics": {
            "splits": len([c for c in island_changes if c["type"] == "split"]),
            "total_affected_nodes": sum(c.get("original_node_count", 0) for c in island_changes)
        }
    }

    with open(output_newislands, "w", encoding="utf-8") as f:
        json.dump(change_summary, f, ensure_ascii=False, indent=2)

    logger.info(f"岛屿变化分析完成:")
    logger.info(f"  发现 {len(island_changes)} 个显著变化")
    logger.info(f"  影响节点总数: {change_summary['statistics']['total_affected_nodes']}")
    logger.info(f"  详细结果已保存到: {output_newislands}")
    logger.info("="*60)


def run_island_comparison(clean_topology_path=None, maintenance_topology_path=None):
    """运行岛屿比较分析的主函数"""
    # 默认文件路径
    if not clean_topology_path:
        clean_topology_path = "../out/clean_topology.json"
    if not maintenance_topology_path:
        maintenance_topology_path = "../out/maintenance_topology.json"

    # 输出文件
    output_original = "../out/original_islands.json"
    output_maintenance = "../out/maintenance_islands.json"
    output_newislands = "../out/new_islands_due_to_maintenance.json"

    logger.info(f"开始岛屿比较分析")
    logger.info(f"输入文件:")
    logger.info(f"  原始拓扑: {clean_topology_path}")
    logger.info(f"  检修拓扑: {maintenance_topology_path}")

    try:
        # 读取拓扑数据
        logger.info("读取拓扑数据...")
        with open(clean_topology_path, "r", encoding="utf-8") as f:
            clean_data = json.load(f)
        with open(maintenance_topology_path, "r", encoding="utf-8") as f:
            maint_data = json.load(f)

        logger.info(f"原始拓扑: {len(clean_data['nodes'])} 节点, {len(clean_data['edges'])} 边")
        logger.info(f"检修拓扑: {len(maint_data['nodes'])} 节点, {len(maint_data['edges'])} 边")

        # 节点 & 边
        nodes_clean = {n["id"]: n for n in clean_data["nodes"]}
        edges_clean = clean_data["edges"]

        nodes_maint = {n["id"]: n for n in maint_data["nodes"]}
        edges_maint = maint_data["edges"]

        # 原始岛划分
        logger.info("分析原始拓扑岛屿...")
        grid_original = GridIslands(nodes_clean, edges_clean, consider_status=True)
        grid_original.find_islands()

        # 检修岛划分
        logger.info("分析检修拓扑岛屿...")
        grid_maint = GridIslands(nodes_maint, edges_maint, consider_status=True)
        grid_maint.find_islands()

        # 调用比较方法
        compare_islands(
            grid_original=grid_original,
            grid_maint=grid_maint,
            maintenance_nodes=nodes_maint,
            maintenance_edges=edges_maint,
            output_original=output_original,
            output_maintenance=output_maintenance,
            output_newislands=output_newislands,
        )

        logger.info("岛屿比较分析完成!")
        return True

    except FileNotFoundError as e:
        logger.error(f"文件未找到: {e}")
        return False
    except json.JSONDecodeError as e:
        logger.error(f"JSON解析错误: {e}")
        return False
    except Exception as e:
        logger.error(f"分析过程中发生错误: {e}")
        return False


if __name__ == "__main__":
    import sys

    # 支持命令行参数
    clean_path = sys.argv[1] if len(sys.argv) > 1 else None
    maint_path = sys.argv[2] if len(sys.argv) > 2 else None

    success = run_island_comparison(clean_path, maint_path)
    sys.exit(0 if success else 1)

# compare_islands.py
import json
from collections import defaultdict, deque


class GridIslands:
    def __init__(self, nodes, edges, consider_status=True):
        if isinstance(nodes, list):
            self.nodes = {n["id"]: n for n in nodes}
        elif isinstance(nodes, dict):
            self.nodes = nodes
        else:
            raise ValueError("nodes must be a list or dict")

        self.edges = edges
        self.consider_status = consider_status
        self.graph = defaultdict(list)
        self.islands = []

        self.build_graph()

    def build_graph(self):
        """构建邻接表，同时记录断开的边"""
        self.disconnected_edges = []

        for edge in self.edges:
            f = edge["from"].split(" ")[0]
            t = edge["to"].split(" ")[0]

            if f not in self.nodes or t not in self.nodes:
                continue

            if self.consider_status:
                devices_status = edge.get("devices_status", [])
                open_devices = [dev["name"] for dev in devices_status if dev.get("status") == "OPEN"]
                if open_devices:
                    self.disconnected_edges.append({
                        "from": f,
                        "to": t,
                        "devices": open_devices
                    })
                    continue

            self.graph[f].append(t)
            self.graph[t].append(f)

    def find_islands(self):
        visited = set()
        islands = []

        for node_id in self.nodes:
            if node_id in visited:
                continue

            island_nodes = []
            queue = deque([node_id])
            visited.add(node_id)

            while queue:
                current = queue.popleft()
                island_nodes.append(current)
                for neighbor in self.graph[current]:
                    if neighbor not in visited:
                        visited.add(neighbor)
                        queue.append(neighbor)

            islands.append(island_nodes)

        self.islands = islands
        return islands

    def get_island_map(self):
        island_map = {}
        for idx, isl in enumerate(self.islands, 1):
            for node_id in isl:
                island_map[node_id] = idx
        return island_map

    def print_islands_summary(self):
        islands = self.islands
        print(f"有效岛数量: {len(islands)}")
        for idx, isl in enumerate(islands, 1):
            print(f"\n岛 {idx}: 节点数量 {len(isl)}")
            for nid in isl:
                node_name = self.nodes[nid].get("name")
                print(f"  - {nid}: {node_name}")

    def save_islands_to_file(self, filepath):
        islands_data = []
        for idx, isl in enumerate(self.islands, 1):
            nodes_info = []
            for nid in isl:
                node_name = self.nodes[nid].get("name")
                nodes_info.append({"id": nid, "name": node_name})
            islands_data.append({
                "island_index": idx,
                "node_count": len(isl),
                "nodes": nodes_info
            })
        with open(filepath, "w", encoding="utf-8") as f:
            json.dump(islands_data, f, ensure_ascii=False, indent=2)
        print(f"岛划分结果已保存到 {filepath}")


from collections import defaultdict


def compare_islands(grid_original, grid_maint, maintenance_nodes, maintenance_edges,
                    output_original, output_maintenance, output_newislands):
    # 保存原始/检修岛结果
    grid_original.save_islands_to_file(output_original)
    grid_maint.save_islands_to_file(output_maintenance)

    original_island_map = grid_original.get_island_map()
    maintenance_island_map = grid_maint.get_island_map()

    # 找出检修导致的变化
    island_changes = []
    
    for orig_idx, orig_nodes in enumerate(grid_original.islands, 1):
        # 找到这些节点在检修后的分布
        new_distribution = {}
        for nid in orig_nodes:
            if nid in maintenance_island_map:
                new_idx = maintenance_island_map[nid]
                if new_idx not in new_distribution:
                    new_distribution[new_idx] = []
                new_distribution[new_idx].append(nid)
        
        # 如果拆分成多个岛
        if len(new_distribution) > 1:
            # 找到真正导致拆分的断开边
            split_edges = []
            for e in grid_maint.disconnected_edges:
                from_island = maintenance_island_map.get(e["from"])
                to_island = maintenance_island_map.get(e["to"])
                if (from_island in new_distribution and 
                    to_island in new_distribution and 
                    from_island != to_island):
                    split_edges.append(e)
            
            island_changes.append({
                "type": "split",
                "original_island": orig_idx,
                "new_islands": new_distribution,
                "split_edges": split_edges
            })
            
            # 控制台打印
            print(f"\n⚡ 原岛 {orig_idx} 被拆分为 {len(new_distribution)} 个岛:")
            for new_idx, nodes in new_distribution.items():
                print(f"  新岛 {new_idx}: {len(nodes)} 个节点")

    # 保存结果
    with open(output_newislands, "w", encoding="utf-8") as f:
        json.dump(island_changes, f, ensure_ascii=False, indent=2)

    print(f"\n原始拓扑岛数量: {len(grid_original.islands)}")
    print(f"检修拓扑岛数量: {len(grid_maint.islands)}")
    print(f"因检修导致的岛变化数量: {len(island_changes)}")
    print(f"岛变化信息已保存到: {output_newislands}")


# main_compare.py
import json
from compare_islands import GridIslands, compare_islands

if __name__ == "__main__":
    # 输入文件
    clean_topology_path = "../out/clean_topology.json"
    maintenance_topology_path = "../out/maintenance_topology.json"

    # 输出文件
    output_original = "../out/original_islands.json"
    output_maintenance = "../out/maintenance_islands.json"
    output_newislands = "../out/new_islands_due_to_maintenance.json"

    # 读取拓扑
    with open(clean_topology_path, "r", encoding="utf-8") as f:
        clean_data = json.load(f)
    with open(maintenance_topology_path, "r", encoding="utf-8") as f:
        maint_data = json.load(f)

    # 节点 & 边
    nodes_clean = {n["id"]: n for n in clean_data["nodes"]}
    edges_clean = clean_data["edges"]

    nodes_maint = {n["id"]: n for n in maint_data["nodes"]}
    edges_maint = maint_data["edges"]

    # 原始岛划分
    grid_original = GridIslands(nodes_clean, edges_clean, consider_status=True)
    grid_original.find_islands()

    # 检修岛划分
    grid_maint = GridIslands(nodes_maint, edges_maint, consider_status=True)
    grid_maint.find_islands()

    # 调用比较方法
    compare_islands(
        grid_original=grid_original,
        grid_maint=grid_maint,
        maintenance_nodes=nodes_maint,
        maintenance_edges=edges_maint,
        output_original=output_original,
        output_maintenance=output_maintenance,
        output_newislands=output_newislands,
    )

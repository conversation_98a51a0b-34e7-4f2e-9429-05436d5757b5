import json


def apply_maintenance(clean_topology_path, output_path, maintenance_devices):
    """
    根据检修设备/节点修改拓扑边和节点状态，并生成新的 JSON
    maintenance_devices: [{"type": "Busbar"/"Winding"/"Load"/"Line", "name": "..."}]
    """
    # 构建检修设备/节点 name 集合
    maintenance_names = set(dev["name"] for dev in maintenance_devices)

    with open(clean_topology_path, "r", encoding="utf-8") as f:
        data = json.load(f)

    # --- 修改节点状态 ---
    nodes = data.get("nodes", [])
    for node in nodes:
        node_name = node.get("matched_name") or node.get("name")
        if node_name in maintenance_names:
            if node.get("status") != "OPEN":
                node["status"] = "OPEN"
                print(f"节点 {node_name} 设置为检修状态 (OPEN)")

    # --- 修改边上设备状态 ---
    edges = data.get("edges", [])
    for edge in edges:
        if not isinstance(edge, dict):
            continue
        devices_status = edge.get("devices_status", [])
        for dev in devices_status:
            dev_name = dev.get("name")
            if dev_name in maintenance_names:
                if dev.get("status") != "OPEN":
                    dev["status"] = "OPEN"
                    print(f"设备 {dev_name} 设置为检修状态 (OPEN)")

    # 保存新的拓扑 JSON
    with open(output_path, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    print(f"已生成带检修状态的拓扑 JSON: {output_path}")


if __name__ == "__main__":
    # 示例检修设备/节点
    maintenance_devices = [
        {"name": "重庆.万州.城北站/10kV.901手车刀闸"}
    ]

    apply_maintenance(
        clean_topology_path="../out/clean_topology.json",
        output_path="../out/maintenance_topology.json",
        maintenance_devices=maintenance_devices
    )

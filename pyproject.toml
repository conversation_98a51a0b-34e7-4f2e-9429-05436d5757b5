[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "grid-topology-analyzer"
version = "1.0.0"
description = "电网拓扑分析系统 - 用于电网检修影响分析和岛屿变化检测"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "Grid Analysis Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "Grid Analysis Team", email = "<EMAIL>"}
]
keywords = ["grid", "topology", "analysis", "power-system", "maintenance"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Science/Research",
    "Topic :: Scientific/Engineering",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
requires-python = ">=3.8"
dependencies = [
    "pyyaml>=6.0",
    "typing-extensions>=4.0.0; python_version<'3.10'",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-mock>=3.7.0",
    "black>=22.0.0",
    "isort>=5.10.0",
    "flake8>=4.0.0",
    "mypy>=0.950",
]
docs = [
    "sphinx>=4.5.0",
    "sphinx-rtd-theme>=1.0.0",
    "myst-parser>=0.18.0",
]
viz = [
    "matplotlib>=3.5.0",
    "plotly>=5.0.0",
    "networkx>=2.6.0",
]

[project.urls]
Homepage = "https://github.com/your-org/grid-topology-analyzer"
Documentation = "https://grid-topology-analyzer.readthedocs.io"
Repository = "https://github.com/your-org/grid-topology-analyzer.git"
Issues = "https://github.com/your-org/grid-topology-analyzer/issues"

[project.scripts]
grid-analyzer = "grid_analyzer.cli:main"
grid-maintenance = "grid_analyzer.maintenance.cli:main"
grid-islands = "grid_analyzer.islands.cli:main"

[tool.setuptools.packages.find]
include = ["grid_analyzer*"]
exclude = ["tests*"]

[tool.setuptools.package-data]
grid_analyzer = [
    "config/*.yaml",
    "templates/*.md",
]

# Black 配置
[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# isort 配置
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["grid_analyzer"]

# pytest 配置
[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

# Coverage 配置
[tool.coverage.run]
source = ["grid_analyzer"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

# MyPy 配置
[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "yaml.*",
]
ignore_missing_imports = true

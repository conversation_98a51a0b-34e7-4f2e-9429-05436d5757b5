# 电网拓扑分析系统 (Grid Topology Analyzer)

[![Python Version](https://img.shields.io/badge/python-3.8%2B-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Code Style](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)

电网拓扑分析系统是一个专业的电力系统分析工具，用于电网检修影响分析、岛屿变化检测和拓扑结构分析。

## 🚀 主要功能

### 1. 拓扑数据处理
- **CIME格式解析**: 支持CIME电网模型文件解析
- **QS格式解析**: 支持QS状态文件解析和处理
- **数据清洗**: 自动清理和标准化拓扑数据
- **名称匹配**: 智能设备名称匹配和映射

### 2. 检修影响分析
- **设备状态更新**: 模拟检修设备的状态变化
- **连通性分析**: 分析检修对电网连通性的影响
- **影响评估**: 量化检修操作的影响范围

### 3. 岛屿变化检测
- **岛屿识别**: 基于图论算法的电网岛屿自动识别
- **变化分析**: 检修前后岛屿变化的详细分析
- **拆分检测**: 识别岛屿拆分和合并情况
- **边缘分析**: 区分新断开边和原本断开边

### 4. 报告生成
- **详细报告**: 生成专业的分析报告
- **可视化输出**: 支持多种格式的结果输出
- **统计分析**: 提供全面的统计信息

## 📦 安装

### 使用pip安装
```bash
pip install grid-topology-analyzer
```

### 从源码安装
```bash
git clone https://github.com/your-org/grid-topology-analyzer.git
cd grid-topology-analyzer
pip install -e .
```

### 开发环境安装
```bash
git clone https://github.com/your-org/grid-topology-analyzer.git
cd grid-topology-analyzer
pip install -e ".[dev]"
```

## 🔧 快速开始

### 1. 基本使用

```python
from grid_analyzer import GridAnalyzer

# 创建分析器实例
analyzer = GridAnalyzer()

# 加载拓扑数据
analyzer.load_topology("data/topology.json")

# 应用检修计划
maintenance_plan = [
    {"name": "重庆.万州.城北站/10kV.901手车刀闸"}
]
analyzer.apply_maintenance(maintenance_plan)

# 分析岛屿变化
changes = analyzer.analyze_island_changes()

# 生成报告
analyzer.generate_report("output/analysis_report.md")
```

### 2. 命令行使用

```bash
# 完整分析流程
grid-analyzer analyze --input data/topology.json --maintenance maintenance.json --output results/

# 仅检修影响分析
grid-maintenance apply --topology data/topology.json --devices "设备1,设备2" --output maintenance_result.json

# 仅岛屿分析
grid-islands compare --original original.json --maintenance maintenance.json --output islands_report.md
```

## 📁 项目结构

```
grid-topology-analyzer/
├── grid_analyzer/              # 主要代码包
│   ├── __init__.py
│   ├── core/                   # 核心功能模块
│   │   ├── __init__.py
│   │   ├── analyzer.py         # 主分析器
│   │   └── graph.py           # 图论算法
│   ├── parsers/               # 数据解析器
│   │   ├── __init__.py
│   │   ├── cime_parser.py     # CIME格式解析
│   │   └── qs_parser.py       # QS格式解析
│   ├── maintenance/           # 检修分析模块
│   │   ├── __init__.py
│   │   ├── topology.py        # 拓扑状态更新
│   │   └── cli.py            # 命令行接口
│   ├── islands/              # 岛屿分析模块
│   │   ├── __init__.py
│   │   ├── detector.py       # 岛屿检测
│   │   ├── analyzer.py       # 变化分析
│   │   └── cli.py           # 命令行接口
│   ├── utils/               # 工具模块
│   │   ├── __init__.py
│   │   ├── logger.py        # 日志配置
│   │   ├── config.py        # 配置管理
│   │   └── validators.py    # 数据验证
│   ├── reports/             # 报告生成
│   │   ├── __init__.py
│   │   ├── generator.py     # 报告生成器
│   │   └── templates/       # 报告模板
│   ├── config/              # 配置文件
│   │   └── default.yaml
│   └── cli.py              # 主命令行接口
├── tests/                   # 测试代码
├── docs/                   # 文档
├── examples/               # 示例代码
├── data/                   # 示例数据
├── requirements.txt        # 依赖包
├── setup.py               # 安装脚本
├── README.md              # 项目说明
├── LICENSE                # 许可证
└── .gitignore            # Git忽略文件
```

## 🧪 测试

```bash
# 运行所有测试
pytest

# 运行测试并生成覆盖率报告
pytest --cov=grid_analyzer --cov-report=html

# 运行特定测试
pytest tests/test_maintenance.py
```

## 📖 文档

详细文档请访问: [https://grid-topology-analyzer.readthedocs.io](https://grid-topology-analyzer.readthedocs.io)

### 本地构建文档
```bash
cd docs
make html
```

## 🤝 贡献

我们欢迎任何形式的贡献！请查看 [CONTRIBUTING.md](CONTRIBUTING.md) 了解详细信息。

### 开发流程
1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

### 代码规范
```bash
# 代码格式化
black grid_analyzer/
isort grid_analyzer/

# 代码检查
flake8 grid_analyzer/
mypy grid_analyzer/
```

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- 感谢所有贡献者的努力
- 感谢电力系统专家的专业指导
- 感谢开源社区的支持

## 📞 联系我们

- 项目主页: https://github.com/your-org/grid-topology-analyzer
- 问题反馈: https://github.com/your-org/grid-topology-analyzer/issues
- 邮箱: <EMAIL>

---

**注意**: 本系统仅用于研究和分析目的，实际电网操作请遵循相关安全规范。

"""
主分析器类 - 电网拓扑分析的核心接口
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Optional, Union, Any

from ..utils.logger import get_logger
from ..utils.config import Config
from ..utils.validators import validate_topology_data, validate_maintenance_plan
from ..maintenance.topology import MaintenanceAnalyzer
from ..islands.detector import IslandDetector
from ..islands.analyzer import IslandChangeAnalyzer
from ..reports.generator import ReportGenerator


class GridAnalyzer:
    """
    电网拓扑分析器主类
    
    提供完整的电网拓扑分析功能，包括：
    - 拓扑数据加载和验证
    - 检修影响分析
    - 岛屿变化检测
    - 报告生成
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化分析器
        
        Args:
            config_path: 配置文件路径，如果为None则使用默认配置
        """
        self.logger = get_logger(self.__class__.__name__)
        self.config = Config(config_path)
        
        # 初始化子模块
        self.maintenance_analyzer = MaintenanceAnalyzer(self.config)
        self.island_detector = IslandDetector(self.config)
        self.island_change_analyzer = IslandChangeAnalyzer(self.config)
        self.report_generator = ReportGenerator(self.config)
        
        # 数据存储
        self.original_topology: Optional[Dict] = None
        self.maintenance_topology: Optional[Dict] = None
        self.analysis_results: Dict[str, Any] = {}
        
        self.logger.info("GridAnalyzer initialized successfully")
    
    def load_topology(self, topology_path: Union[str, Path]) -> None:
        """
        加载拓扑数据
        
        Args:
            topology_path: 拓扑文件路径
            
        Raises:
            FileNotFoundError: 文件不存在
            ValueError: 数据格式错误
        """
        topology_path = Path(topology_path)
        
        if not topology_path.exists():
            raise FileNotFoundError(f"Topology file not found: {topology_path}")
        
        self.logger.info(f"Loading topology from: {topology_path}")
        
        try:
            with open(topology_path, 'r', encoding='utf-8') as f:
                self.original_topology = json.load(f)
            
            # 验证数据格式
            validate_topology_data(self.original_topology)
            
            self.logger.info(f"Topology loaded successfully: "
                           f"{len(self.original_topology.get('nodes', []))} nodes, "
                           f"{len(self.original_topology.get('edges', []))} edges")
            
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON format: {e}")
        except Exception as e:
            raise ValueError(f"Failed to load topology: {e}")
    
    def apply_maintenance(self, maintenance_plan: List[Dict[str, str]], 
                         output_path: Optional[Union[str, Path]] = None) -> Dict:
        """
        应用检修计划
        
        Args:
            maintenance_plan: 检修计划列表，每个元素包含设备信息
            output_path: 输出文件路径，如果为None则不保存文件
            
        Returns:
            检修应用结果
            
        Raises:
            ValueError: 检修计划格式错误或拓扑数据未加载
        """
        if self.original_topology is None:
            raise ValueError("Topology data not loaded. Call load_topology() first.")
        
        # 验证检修计划
        validate_maintenance_plan(maintenance_plan)
        
        self.logger.info(f"Applying maintenance plan with {len(maintenance_plan)} devices")
        
        # 应用检修
        result = self.maintenance_analyzer.apply_maintenance(
            self.original_topology, 
            maintenance_plan
        )
        
        self.maintenance_topology = result["topology"]
        
        # 保存结果
        if output_path:
            output_path = Path(output_path)
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(self.maintenance_topology, f, ensure_ascii=False, indent=2)
            self.logger.info(f"Maintenance topology saved to: {output_path}")
        
        # 存储分析结果
        self.analysis_results["maintenance"] = result
        
        return result
    
    def analyze_island_changes(self, output_path: Optional[Union[str, Path]] = None) -> Dict:
        """
        分析岛屿变化
        
        Args:
            output_path: 输出文件路径，如果为None则不保存文件
            
        Returns:
            岛屿变化分析结果
            
        Raises:
            ValueError: 拓扑数据未加载或检修未应用
        """
        if self.original_topology is None:
            raise ValueError("Original topology not loaded. Call load_topology() first.")
        
        if self.maintenance_topology is None:
            raise ValueError("Maintenance not applied. Call apply_maintenance() first.")
        
        self.logger.info("Analyzing island changes")
        
        # 检测原始拓扑的岛屿
        original_islands = self.island_detector.detect_islands(self.original_topology)
        
        # 检测检修后拓扑的岛屿
        maintenance_islands = self.island_detector.detect_islands(self.maintenance_topology)
        
        # 分析变化
        changes = self.island_change_analyzer.analyze_changes(
            self.original_topology,
            self.maintenance_topology,
            original_islands,
            maintenance_islands
        )
        
        # 保存结果
        if output_path:
            output_path = Path(output_path)
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(changes, f, ensure_ascii=False, indent=2)
            self.logger.info(f"Island changes saved to: {output_path}")
        
        # 存储分析结果
        self.analysis_results["islands"] = {
            "original": original_islands,
            "maintenance": maintenance_islands,
            "changes": changes
        }
        
        return changes
    
    def generate_report(self, output_path: Union[str, Path], 
                       format: str = "markdown") -> Path:
        """
        生成分析报告
        
        Args:
            output_path: 输出文件路径
            format: 报告格式 ("markdown", "html", "json")
            
        Returns:
            生成的报告文件路径
            
        Raises:
            ValueError: 分析结果不完整或格式不支持
        """
        if not self.analysis_results:
            raise ValueError("No analysis results available. Run analysis first.")
        
        output_path = Path(output_path)
        
        self.logger.info(f"Generating {format} report to: {output_path}")
        
        # 生成报告
        report_path = self.report_generator.generate_report(
            self.analysis_results,
            output_path,
            format
        )
        
        self.logger.info(f"Report generated successfully: {report_path}")
        
        return report_path
    
    def run_complete_analysis(self, 
                            topology_path: Union[str, Path],
                            maintenance_plan: List[Dict[str, str]],
                            output_dir: Union[str, Path]) -> Dict[str, Path]:
        """
        运行完整的分析流程
        
        Args:
            topology_path: 拓扑文件路径
            maintenance_plan: 检修计划
            output_dir: 输出目录
            
        Returns:
            生成的文件路径字典
        """
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger.info("Starting complete analysis workflow")
        
        # 1. 加载拓扑
        self.load_topology(topology_path)
        
        # 2. 应用检修
        maintenance_path = output_dir / "maintenance_topology.json"
        self.apply_maintenance(maintenance_plan, maintenance_path)
        
        # 3. 分析岛屿变化
        changes_path = output_dir / "island_changes.json"
        self.analyze_island_changes(changes_path)
        
        # 4. 生成报告
        report_path = output_dir / "analysis_report.md"
        self.generate_report(report_path)
        
        results = {
            "maintenance_topology": maintenance_path,
            "island_changes": changes_path,
            "report": report_path
        }
        
        self.logger.info("Complete analysis workflow finished successfully")
        
        return results
    
    def get_summary(self) -> Dict[str, Any]:
        """
        获取分析摘要
        
        Returns:
            分析摘要信息
        """
        summary = {
            "topology_loaded": self.original_topology is not None,
            "maintenance_applied": self.maintenance_topology is not None,
            "analysis_completed": bool(self.analysis_results)
        }
        
        if self.original_topology:
            summary["original_topology"] = {
                "nodes": len(self.original_topology.get("nodes", [])),
                "edges": len(self.original_topology.get("edges", []))
            }
        
        if "maintenance" in self.analysis_results:
            maintenance_result = self.analysis_results["maintenance"]
            summary["maintenance"] = {
                "devices_updated": maintenance_result.get("devices_updated", 0),
                "nodes_updated": maintenance_result.get("nodes_updated", 0)
            }
        
        if "islands" in self.analysis_results:
            islands_result = self.analysis_results["islands"]
            changes = islands_result.get("changes", {})
            summary["islands"] = {
                "original_count": len(islands_result.get("original", {}).get("islands", [])),
                "maintenance_count": len(islands_result.get("maintenance", {}).get("islands", [])),
                "changes_count": len(changes.get("changes", []))
            }
        
        return summary

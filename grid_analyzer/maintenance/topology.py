"""
检修拓扑分析模块 - 重构自 transfer/maintenance_topology.py

保持原有业务逻辑完全不变，仅进行工程化改进。
"""

import json
from pathlib import Path
from typing import Dict, List, Any, Union, Tuple


def apply_maintenance(clean_topology_path: Union[str, Path],
                     output_path: Union[str, Path],
                     maintenance_devices: List[Dict[str, str]]) -> None:
    """
    根据检修设备/节点修改拓扑边和节点状态，并生成新的 JSON

    Args:
        clean_topology_path: 清洁拓扑文件路径
        output_path: 输出文件路径
        maintenance_devices: 检修设备列表，格式: [{"name": "设备名称"}]
    """
    # 构建检修设备/节点 name 集合
    maintenance_names = set(dev["name"] for dev in maintenance_devices)

    with open(clean_topology_path, "r", encoding="utf-8") as f:
        data = json.load(f)

    # --- 修改节点状态 ---
    nodes = data.get("nodes", [])
    nodes_updated = 0
    for node in nodes:
        # 检查两个可能的名称字段
        node_name = node.get("name")
        matched_name = node.get("matched_name")

        # 如果任一名称匹配检修列表，则设置为检修状态
        if (node_name in maintenance_names) or (matched_name in maintenance_names):
            if node.get("status") != "OPEN":
                original_status = node.get("status", "UNKNOWN")
                node["status"] = "OPEN"
                display_name = matched_name or node_name
                print(f"节点 {display_name} 状态从 {original_status} 设置为检修状态 (OPEN)")
                nodes_updated += 1

    # --- 修改边上设备状态 ---
    edges = data.get("edges", [])
    devices_updated = 0
    for edge in edges:
        if not isinstance(edge, dict):
            continue
        devices_status = edge.get("devices_status", [])
        for dev in devices_status:
            # 检查两个可能的名称字段
            dev_name = dev.get("name")
            matched_name = dev.get("matched_name")

            # 如果任一名称匹配检修列表，则设置为检修状态
            if (dev_name in maintenance_names) or (matched_name in maintenance_names):
                if dev.get("status") != "OPEN":
                    original_status = dev.get("status", "UNKNOWN")
                    dev["status"] = "OPEN"
                    display_name = matched_name or dev_name
                    print(f"设备 {display_name} 状态从 {original_status} 设置为检修状态 (OPEN)")
                    devices_updated += 1

    # 保存新的拓扑 JSON
    with open(output_path, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

    print(f"\n检修状态更新完成:")
    print(f"- 更新节点数量: {nodes_updated}")
    print(f"- 更新设备数量: {devices_updated}")
    print(f"- 已生成带检修状态的拓扑 JSON: {output_path}")


def validate_maintenance_update(original_path: Union[str, Path],
                               maintenance_path: Union[str, Path],
                               maintenance_devices: List[Dict[str, str]]) -> Tuple[int, int, int]:
    """
    验证检修状态更新的正确性

    Args:
        original_path: 原始拓扑文件路径
        maintenance_path: 检修后拓扑文件路径
        maintenance_devices: 检修设备列表

    Returns:
        (节点变化数, 设备变化数, 遗漏设备数)
    """
    maintenance_names = set(dev["name"] for dev in maintenance_devices)

    with open(original_path, "r", encoding="utf-8") as f:
        original_data = json.load(f)

    with open(maintenance_path, "r", encoding="utf-8") as f:
        maintenance_data = json.load(f)

    print("\n=== 检修状态更新验证 ===")

    # 验证节点状态更新
    original_nodes = {n["id"]: n for n in original_data.get("nodes", [])}
    maintenance_nodes = {n["id"]: n for n in maintenance_data.get("nodes", [])}

    node_changes = 0
    for node_id, orig_node in original_nodes.items():
        maint_node = maintenance_nodes.get(node_id)
        if not maint_node:
            continue

        orig_status = orig_node.get("status")
        maint_status = maint_node.get("status")

        if orig_status != maint_status:
            node_name = maint_node.get("matched_name") or maint_node.get("name")
            print(f"节点状态变化: {node_name} ({orig_status} -> {maint_status})")
            node_changes += 1

    # 验证设备状态更新
    device_changes = 0
    for i, orig_edge in enumerate(original_data.get("edges", [])):
        maint_edge = maintenance_data.get("edges", [])[i] if i < len(maintenance_data.get("edges", [])) else None
        if not maint_edge:
            continue

        orig_devices = orig_edge.get("devices_status", [])
        maint_devices = maint_edge.get("devices_status", [])

        for j, orig_dev in enumerate(orig_devices):
            maint_dev = maint_devices[j] if j < len(maint_devices) else None
            if not maint_dev:
                continue

            orig_status = orig_dev.get("status")
            maint_status = maint_dev.get("status")

            if orig_status != maint_status:
                dev_name = maint_dev.get("matched_name") or maint_dev.get("name")
                print(f"设备状态变化: {dev_name} ({orig_status} -> {maint_status})")
                device_changes += 1

    print(f"\n验证结果:")
    print(f"- 节点状态变化数量: {node_changes}")
    print(f"- 设备状态变化数量: {device_changes}")

    # 检查是否有遗漏的检修设备
    missed_devices = []
    for edge in maintenance_data.get("edges", []):
        for dev in edge.get("devices_status", []):
            dev_name = dev.get("name")
            matched_name = dev.get("matched_name")
            if ((dev_name in maintenance_names or matched_name in maintenance_names)
                and dev.get("status") != "OPEN"):
                missed_devices.append(dev_name or matched_name)

    if missed_devices:
        print(f"\n⚠️  警告: 以下检修设备状态未正确更新:")
        for dev in missed_devices:
            print(f"  - {dev}")
    else:
        print(f"\n✅ 所有检修设备状态已正确更新")

    return node_changes, device_changes, len(missed_devices)


# 为了向后兼容，保留 MaintenanceAnalyzer 类
class MaintenanceAnalyzer:
    """检修分析器类 - 向后兼容包装器"""

    def __init__(self, config=None):
        """初始化检修分析器"""
        self.config = config

    def apply_maintenance(self, topology: Dict[str, Any],
                         maintenance_devices: List[Dict[str, str]]) -> Dict[str, Any]:
        """
        应用检修计划到拓扑数据

        Args:
            topology: 原始拓扑数据
            maintenance_devices: 检修设备列表

        Returns:
            包含更新后拓扑和统计信息的字典
        """
        # 使用临时文件进行处理
        import tempfile
        import os

        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as temp_input:
            json.dump(topology, temp_input, ensure_ascii=False, indent=2)
            temp_input_path = temp_input.name

        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as temp_output:
            temp_output_path = temp_output.name

        try:
            # 应用检修
            apply_maintenance(temp_input_path, temp_output_path, maintenance_devices)

            # 读取结果
            with open(temp_output_path, 'r', encoding='utf-8') as f:
                updated_topology = json.load(f)

            # 验证更新
            node_changes, device_changes, missed_count = validate_maintenance_update(
                temp_input_path, temp_output_path, maintenance_devices
            )

            return {
                "topology": updated_topology,
                "statistics": {
                    "nodes_updated": node_changes,
                    "devices_updated": device_changes,
                    "missed_devices": missed_count,
                    "maintenance_devices": [dev["name"] for dev in maintenance_devices]
                },
                "maintenance_devices": maintenance_devices
            }

        finally:
            # 清理临时文件
            try:
                os.unlink(temp_input_path)
                os.unlink(temp_output_path)
            except:
                pass


def validate_maintenance_update(original_path: Union[str, Path], 
                               maintenance_path: Union[str, Path], 
                               maintenance_devices: List[Dict[str, str]],
                               config: Optional[Config] = None) -> Dict[str, Any]:
    """
    验证检修状态更新的正确性（保持原有逻辑）
    
    Args:
        original_path: 原始拓扑文件路径
        maintenance_path: 检修后拓扑文件路径
        maintenance_devices: 检修设备列表
        config: 配置对象
        
    Returns:
        验证结果
    """
    logger = get_logger(__name__)
    maintenance_names = set(dev["name"] for dev in maintenance_devices)

    with open(original_path, "r", encoding="utf-8") as f:
        original_data = json.load(f)

    with open(maintenance_path, "r", encoding="utf-8") as f:
        maintenance_data = json.load(f)

    logger.info("=== 检修状态更新验证 ===")

    # 验证节点状态更新
    original_nodes = {n["id"]: n for n in original_data.get("nodes", [])}
    maintenance_nodes = {n["id"]: n for n in maintenance_data.get("nodes", [])}

    node_changes = 0
    node_change_details = []
    
    for node_id, orig_node in original_nodes.items():
        maint_node = maintenance_nodes.get(node_id)
        if not maint_node:
            continue

        orig_status = orig_node.get("status")
        maint_status = maint_node.get("status")

        if orig_status != maint_status:
            node_name = maint_node.get("matched_name") or maint_node.get("name")
            change_detail = f"节点状态变化: {node_name} ({orig_status} -> {maint_status})"
            logger.info(change_detail)
            node_change_details.append(change_detail)
            node_changes += 1

    # 验证设备状态更新
    device_changes = 0
    device_change_details = []
    
    for i, orig_edge in enumerate(original_data.get("edges", [])):
        maint_edge = maintenance_data.get("edges", [])[i] if i < len(maintenance_data.get("edges", [])) else None
        if not maint_edge:
            continue

        orig_devices = orig_edge.get("devices_status", [])
        maint_devices = maint_edge.get("devices_status", [])

        for j, orig_dev in enumerate(orig_devices):
            maint_dev = maint_devices[j] if j < len(maint_devices) else None
            if not maint_dev:
                continue

            orig_status = orig_dev.get("status")
            maint_status = maint_dev.get("status")

            if orig_status != maint_status:
                dev_name = maint_dev.get("matched_name") or maint_dev.get("name")
                change_detail = f"设备状态变化: {dev_name} ({orig_status} -> {maint_status})"
                logger.info(change_detail)
                device_change_details.append(change_detail)
                device_changes += 1

    # 验证结果
    result = {
        "node_changes": node_changes,
        "device_changes": device_changes,
        "node_change_details": node_change_details,
        "device_change_details": device_change_details,
        "maintenance_devices_count": len(maintenance_devices),
        "validation_passed": True
    }

    logger.info(f"验证结果:")
    logger.info(f"- 节点状态变化数量: {node_changes}")
    logger.info(f"- 设备状态变化数量: {device_changes}")

    # 检查是否所有检修设备都被正确更新
    missing_devices = []
    for device in maintenance_devices:
        device_name = device["name"]
        found = False
        
        # 在设备变化中查找
        for detail in device_change_details:
            if device_name in detail and "OPEN" in detail:
                found = True
                break
        
        if not found:
            missing_devices.append(device_name)

    if missing_devices:
        logger.warning(f"以下检修设备未找到状态变化: {missing_devices}")
        result["missing_devices"] = missing_devices
        result["validation_passed"] = False
    else:
        logger.info("✅ 所有检修设备状态已正确更新")

    return result

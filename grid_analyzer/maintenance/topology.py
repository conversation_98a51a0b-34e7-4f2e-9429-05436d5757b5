"""
检修拓扑分析模块 - 重构自 transfer/maintenance_topology.py

保持原有业务逻辑不变，增加工程化改进。
"""

import json
import copy
from pathlib import Path
from typing import Dict, List, Any, Optional, Union

from ..utils.logger import get_logger, LoggerMixin
from ..utils.config import Config
from ..utils.validators import validate_topology_data, validate_maintenance_plan


class MaintenanceAnalyzer(LoggerMixin):
    """检修分析器类"""
    
    def __init__(self, config: Optional[Config] = None):
        """
        初始化检修分析器
        
        Args:
            config: 配置对象
        """
        super().__init__()
        self.config = config or Config()
        
    def apply_maintenance(self, 
                         topology: Dict[str, Any], 
                         maintenance_devices: List[Dict[str, str]]) -> Dict[str, Any]:
        """
        应用检修计划到拓扑数据
        
        Args:
            topology: 原始拓扑数据
            maintenance_devices: 检修设备列表
            
        Returns:
            包含更新后拓扑和统计信息的字典
        """
        # 验证输入数据
        validate_topology_data(topology)
        validate_maintenance_plan(maintenance_devices)
        
        # 深拷贝拓扑数据以避免修改原始数据
        updated_topology = copy.deepcopy(topology)
        
        # 提取检修设备名称
        maintenance_names = set(dev["name"] for dev in maintenance_devices)
        
        self.logger.info(f"开始应用检修计划，涉及 {len(maintenance_names)} 个设备")
        
        # 统计信息
        stats = {
            "nodes_updated": 0,
            "devices_updated": 0,
            "maintenance_devices": list(maintenance_names)
        }
        
        # 更新节点状态
        stats["nodes_updated"] = self._update_node_status(
            updated_topology.get("nodes", []), 
            maintenance_names
        )
        
        # 更新设备状态
        stats["devices_updated"] = self._update_device_status(
            updated_topology.get("edges", []), 
            maintenance_names
        )
        
        self.logger.info(f"检修状态更新完成: 节点 {stats['nodes_updated']} 个, "
                        f"设备 {stats['devices_updated']} 个")
        
        return {
            "topology": updated_topology,
            "statistics": stats,
            "maintenance_devices": maintenance_devices
        }
    
    def _update_node_status(self, nodes: List[Dict], maintenance_names: set) -> int:
        """
        更新节点状态
        
        Args:
            nodes: 节点列表
            maintenance_names: 检修设备名称集合
            
        Returns:
            更新的节点数量
        """
        updated_count = 0
        
        for node in nodes:
            node_name = node.get("name", "")
            matched_name = node.get("matched_name", "")
            
            # 检查两个可能的名称字段
            if (node_name in maintenance_names) or (matched_name in maintenance_names):
                if node.get("status") != "OPEN":
                    original_status = node.get("status", "UNKNOWN")
                    node["status"] = "OPEN"
                    display_name = matched_name or node_name
                    self.logger.info(f"节点 {display_name} 状态从 {original_status} 设置为检修状态 (OPEN)")
                    updated_count += 1
        
        return updated_count
    
    def _update_device_status(self, edges: List[Dict], maintenance_names: set) -> int:
        """
        更新设备状态
        
        Args:
            edges: 边列表
            maintenance_names: 检修设备名称集合
            
        Returns:
            更新的设备数量
        """
        updated_count = 0
        
        for edge in edges:
            devices_status = edge.get("devices_status", [])
            
            for dev in devices_status:
                dev_name = dev.get("name", "")
                matched_name = dev.get("matched_name", "")
                
                # 检查两个可能的名称字段
                if (dev_name in maintenance_names) or (matched_name in maintenance_names):
                    if dev.get("status") != "OPEN":
                        original_status = dev.get("status", "UNKNOWN")
                        dev["status"] = "OPEN"
                        display_name = matched_name or dev_name
                        self.logger.info(f"设备 {display_name} 状态从 {original_status} 设置为检修状态 (OPEN)")
                        updated_count += 1
        
        return updated_count


def apply_maintenance(original_path: Union[str, Path], 
                     output_path: Union[str, Path], 
                     maintenance_devices: List[Dict[str, str]],
                     config: Optional[Config] = None) -> Dict[str, Any]:
    """
    应用检修计划的便捷函数（保持向后兼容）
    
    Args:
        original_path: 原始拓扑文件路径
        output_path: 输出文件路径
        maintenance_devices: 检修设备列表
        config: 配置对象
        
    Returns:
        应用结果
    """
    logger = get_logger(__name__)
    
    # 读取原始拓扑
    with open(original_path, "r", encoding="utf-8") as f:
        original_topology = json.load(f)
    
    # 创建分析器并应用检修
    analyzer = MaintenanceAnalyzer(config)
    result = analyzer.apply_maintenance(original_topology, maintenance_devices)
    
    # 保存结果
    output_config = config.get_section("output") if config else {}
    encoding = output_config.get("encoding", "utf-8")
    indent = output_config.get("json_indent", 2)
    ensure_ascii = not output_config.get("ensure_ascii", True)
    
    with open(output_path, "w", encoding=encoding) as f:
        json.dump(result["topology"], f, ensure_ascii=ensure_ascii, indent=indent)
    
    logger.info(f"检修状态更新完成:")
    logger.info(f"- 更新节点数量: {result['statistics']['nodes_updated']}")
    logger.info(f"- 更新设备数量: {result['statistics']['devices_updated']}")
    logger.info(f"- 已生成带检修状态的拓扑 JSON: {output_path}")
    
    return result


def validate_maintenance_update(original_path: Union[str, Path], 
                               maintenance_path: Union[str, Path], 
                               maintenance_devices: List[Dict[str, str]],
                               config: Optional[Config] = None) -> Dict[str, Any]:
    """
    验证检修状态更新的正确性（保持原有逻辑）
    
    Args:
        original_path: 原始拓扑文件路径
        maintenance_path: 检修后拓扑文件路径
        maintenance_devices: 检修设备列表
        config: 配置对象
        
    Returns:
        验证结果
    """
    logger = get_logger(__name__)
    maintenance_names = set(dev["name"] for dev in maintenance_devices)

    with open(original_path, "r", encoding="utf-8") as f:
        original_data = json.load(f)

    with open(maintenance_path, "r", encoding="utf-8") as f:
        maintenance_data = json.load(f)

    logger.info("=== 检修状态更新验证 ===")

    # 验证节点状态更新
    original_nodes = {n["id"]: n for n in original_data.get("nodes", [])}
    maintenance_nodes = {n["id"]: n for n in maintenance_data.get("nodes", [])}

    node_changes = 0
    node_change_details = []
    
    for node_id, orig_node in original_nodes.items():
        maint_node = maintenance_nodes.get(node_id)
        if not maint_node:
            continue

        orig_status = orig_node.get("status")
        maint_status = maint_node.get("status")

        if orig_status != maint_status:
            node_name = maint_node.get("matched_name") or maint_node.get("name")
            change_detail = f"节点状态变化: {node_name} ({orig_status} -> {maint_status})"
            logger.info(change_detail)
            node_change_details.append(change_detail)
            node_changes += 1

    # 验证设备状态更新
    device_changes = 0
    device_change_details = []
    
    for i, orig_edge in enumerate(original_data.get("edges", [])):
        maint_edge = maintenance_data.get("edges", [])[i] if i < len(maintenance_data.get("edges", [])) else None
        if not maint_edge:
            continue

        orig_devices = orig_edge.get("devices_status", [])
        maint_devices = maint_edge.get("devices_status", [])

        for j, orig_dev in enumerate(orig_devices):
            maint_dev = maint_devices[j] if j < len(maint_devices) else None
            if not maint_dev:
                continue

            orig_status = orig_dev.get("status")
            maint_status = maint_dev.get("status")

            if orig_status != maint_status:
                dev_name = maint_dev.get("matched_name") or maint_dev.get("name")
                change_detail = f"设备状态变化: {dev_name} ({orig_status} -> {maint_status})"
                logger.info(change_detail)
                device_change_details.append(change_detail)
                device_changes += 1

    # 验证结果
    result = {
        "node_changes": node_changes,
        "device_changes": device_changes,
        "node_change_details": node_change_details,
        "device_change_details": device_change_details,
        "maintenance_devices_count": len(maintenance_devices),
        "validation_passed": True
    }

    logger.info(f"验证结果:")
    logger.info(f"- 节点状态变化数量: {node_changes}")
    logger.info(f"- 设备状态变化数量: {device_changes}")

    # 检查是否所有检修设备都被正确更新
    missing_devices = []
    for device in maintenance_devices:
        device_name = device["name"]
        found = False
        
        # 在设备变化中查找
        for detail in device_change_details:
            if device_name in detail and "OPEN" in detail:
                found = True
                break
        
        if not found:
            missing_devices.append(device_name)

    if missing_devices:
        logger.warning(f"以下检修设备未找到状态变化: {missing_devices}")
        result["missing_devices"] = missing_devices
        result["validation_passed"] = False
    else:
        logger.info("✅ 所有检修设备状态已正确更新")

    return result

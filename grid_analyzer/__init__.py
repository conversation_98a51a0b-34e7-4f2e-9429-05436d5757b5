"""
电网拓扑分析系统 (Grid Topology Analyzer)

一个专业的电力系统分析工具，用于电网检修影响分析、岛屿变化检测和拓扑结构分析。
"""

__version__ = "1.0.0"
__author__ = "Grid Analysis Team"
__email__ = "<EMAIL>"

# 主要类的快捷导入 - 延迟导入以避免循环依赖
__all__ = [
    "GridAnalyzer",
    "MaintenanceAnalyzer",
    "IslandDetector",
    "IslandChangeAnalyzer",
    "__version__",
]

def get_version() -> str:
    """获取版本信息"""
    return __version__

def get_info() -> dict:
    """获取项目信息"""
    return {
        "name": "Grid Topology Analyzer",
        "version": __version__,
        "author": __author__,
        "email": __email__,
        "description": "电网拓扑分析系统 - 用于电网检修影响分析和岛屿变化检测"
    }

# 延迟导入主要类
def __getattr__(name: str):
    if name == "GridAnalyzer":
        from .core.analyzer import GridAnalyzer
        return GridAnalyzer
    elif name == "MaintenanceAnalyzer":
        from .maintenance.topology import MaintenanceAnalyzer
        return MaintenanceAnalyzer
    elif name == "IslandDetector":
        from .islands.detector import IslandDetector
        return IslandDetector
    elif name == "IslandChangeAnalyzer":
        from .islands.analyzer import IslandChangeAnalyzer
        return IslandChangeAnalyzer
    else:
        raise AttributeError(f"module '{__name__}' has no attribute '{name}'")

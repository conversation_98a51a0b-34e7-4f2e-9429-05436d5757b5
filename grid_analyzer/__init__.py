"""
电网拓扑分析系统 (Grid Topology Analyzer)

一个专业的电力系统分析工具，用于电网检修影响分析、岛屿变化检测和拓扑结构分析。
"""

__version__ = "1.0.0"
__author__ = "Grid Analysis Team"
__email__ = "<EMAIL>"

from .core.analyzer import GridAnalyzer
from .maintenance.topology import MaintenanceAnalyzer
from .islands.detector import IslandDetector
from .islands.analyzer import IslandChangeAnalyzer

# 主要类的快捷导入
__all__ = [
    "GridAnalyzer",
    "MaintenanceAnalyzer", 
    "IslandDetector",
    "IslandChangeAnalyzer",
    "__version__",
]

# 版本信息
def get_version():
    """获取版本信息"""
    return __version__

def get_info():
    """获取项目信息"""
    return {
        "name": "Grid Topology Analyzer",
        "version": __version__,
        "author": __author__,
        "email": __email__,
        "description": "电网拓扑分析系统 - 用于电网检修影响分析和岛屿变化检测"
    }

"""
岛屿变化分析模块 - 重构自 transfer/compare_islands.py 的分析功能

保持原有业务逻辑不变，增加工程化改进。
"""

from collections import defaultdict
from datetime import datetime
from typing import Dict, List, Any, Optional

from ..utils.logger import get_logger, LoggerMixin
from ..utils.config import Config
from .detector import GridIslands


class IslandChangeAnalyzer(LoggerMixin):
    """岛屿变化分析器"""
    
    def __init__(self, config: Optional[Config] = None):
        """
        初始化岛屿变化分析器
        
        Args:
            config: 配置对象
        """
        super().__init__()
        self.config = config or Config()
    
    def analyze_changes(self, 
                       original_topology: Dict[str, Any],
                       maintenance_topology: Dict[str, Any],
                       original_islands: Dict[str, Any],
                       maintenance_islands: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析岛屿变化
        
        Args:
            original_topology: 原始拓扑数据
            maintenance_topology: 检修后拓扑数据
            original_islands: 原始岛屿数据
            maintenance_islands: 检修后岛屿数据
            
        Returns:
            变化分析结果
        """
        self.logger.info("开始岛屿变化分析")
        
        # 创建GridIslands实例用于详细分析
        consider_status = self.config.get("islands.consider_status", True)
        
        grid_original = GridIslands(
            original_topology.get("nodes", []),
            original_topology.get("edges", []),
            consider_status,
            self.config
        )
        grid_original.find_islands()
        
        grid_maint = GridIslands(
            maintenance_topology.get("nodes", []),
            maintenance_topology.get("edges", []),
            consider_status,
            self.config
        )
        grid_maint.find_islands()
        
        # 分析变化
        changes = self._analyze_island_changes(grid_original, grid_maint)
        
        # 构建完整结果
        result = {
            "analysis_time": datetime.now().isoformat(),
            "original_islands_count": len(grid_original.islands),
            "maintenance_islands_count": len(grid_maint.islands),
            "island_count_change": len(grid_maint.islands) - len(grid_original.islands),
            "disconnected_edges_count": len(grid_maint.disconnected_edges),
            "changes": changes["changes"],
            "statistics": changes["statistics"],
            "disconnected_edges_analysis": self._analyze_disconnected_edges(grid_maint.disconnected_edges)
        }
        
        self.logger.info(f"岛屿变化分析完成: 发现 {len(changes['changes'])} 个显著变化")
        
        return result
    
    def _analyze_island_changes(self, grid_original: GridIslands, grid_maint: GridIslands) -> Dict[str, Any]:
        """详细分析岛屿变化（保持原有逻辑）"""
        original_island_map = grid_original.get_island_map()
        maintenance_island_map = grid_maint.get_island_map()
        
        # 分析各种变化类型
        island_changes = []
        split_count = 0
        merge_count = 0
        unchanged_count = 0
        
        # 1. 分析岛屿拆分
        for orig_idx, orig_nodes in enumerate(grid_original.islands, 1):
            # 找到这些节点在检修后的分布
            new_distribution = {}
            missing_nodes = []
            
            for nid in orig_nodes:
                if nid in maintenance_island_map:
                    new_idx = maintenance_island_map[nid]
                    if new_idx not in new_distribution:
                        new_distribution[new_idx] = []
                    new_distribution[new_idx].append(nid)
                else:
                    missing_nodes.append(nid)
            
            if missing_nodes:
                self.logger.warning(f"原岛 {orig_idx} 中有 {len(missing_nodes)} 个节点在检修后消失")
            
            # 如果拆分成多个岛
            if len(new_distribution) > 1:
                split_count += 1
                
                # 找到导致拆分的断开边 - 改进版本
                split_edges = []
                newly_disconnected_edges = []
                
                # 首先找出所有连接不同新岛的断开边
                for e in grid_maint.disconnected_edges:
                    from_island = maintenance_island_map.get(e["from"])
                    to_island = maintenance_island_map.get(e["to"])
                    if (from_island in new_distribution and 
                        to_island in new_distribution and 
                        from_island != to_island):
                        split_edges.append(e)
                
                # 然后检查哪些边是新断开的（原来连通，现在断开）
                for split_edge in split_edges:
                    edge_idx = split_edge["edge_index"]
                    
                    # 检查原始拓扑中这条边的状态
                    orig_edge = grid_original.edges[edge_idx] if edge_idx < len(grid_original.edges) else None
                    if orig_edge:
                        orig_devices_status = orig_edge.get("devices_status", [])
                        orig_open_devices = [dev["name"] for dev in orig_devices_status if dev.get("status") == "OPEN"]
                        
                        # 如果原始拓扑中这条边就是断开的，标记为"原本断开"
                        if orig_open_devices:
                            split_edge["originally_disconnected"] = True
                            split_edge["original_open_devices"] = orig_open_devices
                            self.logger.debug(f"边 {split_edge['from_name']} <-> {split_edge['to_name']} 在原始拓扑中就是断开的")
                        else:
                            split_edge["originally_disconnected"] = False
                            newly_disconnected_edges.append(split_edge)
                            self.logger.info(f"新断开的边: {split_edge['from_name']} <-> {split_edge['to_name']}")
                    else:
                        split_edge["originally_disconnected"] = None  # 无法确定
                
                # 构建拆分详情
                split_details = []
                for new_idx, nodes in new_distribution.items():
                    island_info = grid_maint.get_island_info(new_idx)
                    split_details.append({
                        "new_island_index": new_idx + 1,  # 1-based索引
                        "node_count": len(nodes),
                        "sample_nodes": island_info.get("sample_nodes", []),
                        "voltage_distribution": island_info.get("voltage_distribution", {}),
                        "type_distribution": island_info.get("type_distribution", {})
                    })
                
                change_info = {
                    "type": "split",
                    "original_island": orig_idx,
                    "original_node_count": len(orig_nodes),
                    "new_islands_count": len(new_distribution),
                    "split_details": split_details,
                    "split_edges": split_edges,
                    "newly_disconnected_edges": newly_disconnected_edges,
                    "impact_score": len(orig_nodes) * len(new_distribution)  # 影响评分
                }
                island_changes.append(change_info)
                
                # 详细日志
                self.logger.info(f"🔥 岛屿拆分 - 原岛 {orig_idx} ({len(orig_nodes)} 节点) -> {len(new_distribution)} 个新岛")
                for detail in split_details:
                    self.logger.info(f"  新岛 {detail['new_island_index']}: {detail['node_count']} 节点, "
                                  f"样例: {', '.join(detail['sample_nodes'])}")
                
                # 区分新断开的边和原本就断开的边
                if newly_disconnected_edges:
                    self.logger.info(f"  🔧 新断开的边 ({len(newly_disconnected_edges)} 条):")
                    for edge in newly_disconnected_edges:
                        self.logger.info(f"    {edge['from_name']} <-> {edge['to_name']}: {edge['open_devices']}")
                
                originally_disconnected = [e for e in split_edges if e.get("originally_disconnected")]
                if originally_disconnected:
                    self.logger.info(f"  ⚠️  原本就断开的边 ({len(originally_disconnected)} 条):")
                    for edge in originally_disconnected[:3]:  # 只显示前3条
                        self.logger.info(f"    {edge['from_name']} <-> {edge['to_name']}: {edge.get('original_open_devices', [])}")
                    if len(originally_disconnected) > 3:
                        self.logger.info(f"    ... 还有 {len(originally_disconnected) - 3} 条原本就断开的边")
            
            elif len(new_distribution) == 1:
                unchanged_count += 1
        
        # 统计信息
        total_affected_nodes = sum(change["original_node_count"] for change in island_changes)
        
        statistics = {
            "splits": split_count,
            "merges": merge_count,
            "unchanged": unchanged_count,
            "total_affected_nodes": total_affected_nodes
        }
        
        self.logger.info("岛屿变化统计:")
        self.logger.info(f"  拆分: {split_count} 个原岛")
        self.logger.info(f"  合并: {merge_count} 个新岛")
        self.logger.info(f"  不变: {unchanged_count} 个岛")
        
        return {
            "changes": island_changes,
            "statistics": statistics
        }
    
    def _analyze_disconnected_edges(self, disconnected_edges: List[Dict]) -> Dict[str, Any]:
        """分析断开边的统计信息"""
        device_type_count = defaultdict(int)
        
        for edge in disconnected_edges:
            for device_name in edge.get("open_devices", []):
                # 简单的设备类型识别
                if "刀闸" in device_name:
                    device_type_count["刀闸"] += 1
                elif "开关" in device_name:
                    device_type_count["开关"] += 1
                else:
                    device_type_count["其他"] += 1
        
        self.logger.info("断开边分析:")
        self.logger.info(f"  总断开边数: {len(disconnected_edges)}")
        self.logger.info(f"  断开设备类型分布: {dict(device_type_count)}")
        
        return {
            "total_count": len(disconnected_edges),
            "device_type_distribution": dict(device_type_count)
        }

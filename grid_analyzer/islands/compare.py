"""
岛屿比较主函数 - 重构自 transfer/compare_islands.py 的主要功能

保持原有业务逻辑不变，增加工程化改进。
"""

import json
from pathlib import Path
from typing import Union, Optional, Dict, Any

from ..utils.logger import get_logger
from ..utils.config import Config
from .detector import IslandDetector
from .analyzer import IslandChangeAnalyzer


def run_island_comparison(original_path: Union[str, Path],
                         maintenance_path: Union[str, Path],
                         output_path: Optional[Union[str, Path]] = None,
                         config: Optional[Config] = None) -> bool:
    """
    运行岛屿比较分析（保持向后兼容的主函数）
    
    Args:
        original_path: 原始拓扑文件路径
        maintenance_path: 检修后拓扑文件路径
        output_path: 输出文件路径，如果为None则使用默认路径
        config: 配置对象
        
    Returns:
        是否成功完成分析
    """
    logger = get_logger(__name__)
    
    if config is None:
        config = Config()
    
    try:
        logger.info("开始岛屿比较分析")
        logger.info("输入文件:")
        logger.info(f"  原始拓扑: {original_path}")
        logger.info(f"  检修拓扑: {maintenance_path}")
        
        # 读取拓扑数据
        logger.info("读取拓扑数据...")
        
        with open(original_path, 'r', encoding='utf-8') as f:
            original_topology = json.load(f)
        
        with open(maintenance_path, 'r', encoding='utf-8') as f:
            maintenance_topology = json.load(f)
        
        logger.info(f"原始拓扑: {len(original_topology.get('nodes', []))} 节点, "
                   f"{len(original_topology.get('edges', []))} 边")
        logger.info(f"检修拓扑: {len(maintenance_topology.get('nodes', []))} 节点, "
                   f"{len(maintenance_topology.get('edges', []))} 边")
        
        # 创建分析器
        island_detector = IslandDetector(config)
        change_analyzer = IslandChangeAnalyzer(config)
        
        # 分析原始拓扑岛屿
        logger.info("分析原始拓扑岛屿...")
        original_islands = island_detector.detect_islands(original_topology)
        
        # 分析检修拓扑岛屿
        logger.info("分析检修拓扑岛屿...")
        maintenance_islands = island_detector.detect_islands(maintenance_topology)
        
        # 保存岛屿分析结果
        logger.info("=" * 60)
        logger.info("开始岛屿比较分析")
        logger.info("=" * 60)
        
        logger.info("保存岛屿分析结果...")
        
        # 确定输出目录
        output_dir = Path(config.get("output.default_dir", "out"))
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存原始岛屿信息
        original_islands_path = output_dir / "original_islands.json"
        _save_island_data(original_islands, original_islands_path, config)
        logger.info(f"岛划分结果已保存 - {len(original_islands['islands'])} 个岛屿，"
                   f"{original_islands['statistics']['total_nodes']} 个节点")
        
        # 保存检修后岛屿信息
        maintenance_islands_path = output_dir / "maintenance_islands.json"
        _save_island_data(maintenance_islands, maintenance_islands_path, config)
        logger.info(f"岛划分结果已保存 - {len(maintenance_islands['islands'])} 个岛屿，"
                   f"{maintenance_islands['statistics']['total_nodes']} 个节点")
        
        # 基本统计
        original_count = len(original_islands['islands'])
        maintenance_count = len(maintenance_islands['islands'])
        change_count = maintenance_count - original_count
        
        logger.info("基本统计:")
        logger.info(f"  原始拓扑岛数量: {original_count}")
        logger.info(f"  检修拓扑岛数量: {maintenance_count}")
        logger.info(f"  岛数量变化: {change_count:+d}")
        
        # 分析岛屿变化
        changes = change_analyzer.analyze_changes(
            original_topology,
            maintenance_topology,
            original_islands,
            maintenance_islands
        )
        
        # 保存变化分析结果
        if output_path is None:
            output_path = output_dir / "new_islands_due_to_maintenance.json"
        
        _save_changes_data(changes, output_path, config)
        
        logger.info(f"岛屿变化分析完成:")
        logger.info(f"  发现 {len(changes['changes'])} 个显著变化")
        logger.info(f"  影响节点总数: {changes['statistics']['total_affected_nodes']}")
        logger.info(f"  详细结果已保存到: {output_path}")
        
        logger.info("=" * 60)
        logger.info("岛屿比较分析完成!")
        
        return True
        
    except Exception as e:
        logger.error(f"岛屿比较分析失败: {e}")
        return False


def _save_island_data(island_data: Dict[str, Any], 
                     output_path: Path, 
                     config: Config) -> None:
    """保存岛屿数据"""
    logger = get_logger(__name__)
    
    output_config = config.get_section("output")
    encoding = output_config.get("encoding", "utf-8")
    indent = output_config.get("json_indent", 2)
    ensure_ascii = not output_config.get("ensure_ascii", True)
    
    logger.info(f"保存岛屿信息到: {output_path}")
    
    with open(output_path, 'w', encoding=encoding) as f:
        json.dump(island_data, f, ensure_ascii=ensure_ascii, indent=indent)


def _save_changes_data(changes_data: Dict[str, Any], 
                      output_path: Path, 
                      config: Config) -> None:
    """保存变化分析数据"""
    output_config = config.get_section("output")
    encoding = output_config.get("encoding", "utf-8")
    indent = output_config.get("json_indent", 2)
    ensure_ascii = not output_config.get("ensure_ascii", True)
    
    with open(output_path, 'w', encoding=encoding) as f:
        json.dump(changes_data, f, ensure_ascii=ensure_ascii, indent=indent)


# 为了向后兼容，保留原有的主函数调用方式
def main():
    """主函数，用于命令行调用"""
    import sys
    
    if len(sys.argv) >= 3:
        original_path = sys.argv[1]
        maintenance_path = sys.argv[2]
        output_path = sys.argv[3] if len(sys.argv) > 3 else None
        
        success = run_island_comparison(original_path, maintenance_path, output_path)
        sys.exit(0 if success else 1)
    else:
        # 使用默认路径
        success = run_island_comparison(
            "../out/clean_topology.json",
            "../out/maintenance_topology.json"
        )
        sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()

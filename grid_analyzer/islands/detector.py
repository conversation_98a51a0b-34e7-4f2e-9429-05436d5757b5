"""
岛屿检测模块 - 重构自 transfer/compare_islands.py 的 GridIslands 类

保持原有业务逻辑不变，增加工程化改进。
"""

from collections import defaultdict, deque
from typing import Dict, List, Any, Optional, Set, Tuple

from ..utils.logger import get_logger, LoggerMixin
from ..utils.config import Config


class GridIslands(LoggerMixin):
    """
    电网岛屿检测类
    
    使用图论算法检测电网中的连通分量（岛屿）
    """
    
    def __init__(self, nodes, edges, consider_status: bool = True, config: Optional[Config] = None):
        """
        初始化岛屿检测器
        
        Args:
            nodes: 节点数据，可以是列表或字典
            edges: 边数据列表
            consider_status: 是否考虑设备状态
            config: 配置对象
        """
        super().__init__()
        self.config = config or Config()
        
        # 处理节点数据格式
        if isinstance(nodes, list):
            self.nodes = {n["id"]: n for n in nodes}
        elif isinstance(nodes, dict):
            self.nodes = nodes
        else:
            raise ValueError("nodes must be a list or dict")

        self.edges = edges
        self.consider_status = consider_status
        self.graph = defaultdict(list)
        self.islands = []
        
        # 边的详细信息
        self.disconnected_edges = []
        self.connected_edges = []
        
        # 构建图
        self.build_graph()

    def build_graph(self):
        """构建邻接表，同时记录断开的边"""
        total_edges = len(self.edges)
        log_limit = self.config.get("islands.log_disconnected_edges_limit", 10)
        
        self.logger.info(f"开始构建图，总边数: {total_edges}")

        for i, edge in enumerate(self.edges):
            f = edge["from"].split(" ")[0]
            t = edge["to"].split(" ")[0]

            # 检查节点是否存在
            if f not in self.nodes or t not in self.nodes:
                self.logger.warning(f"边 {i+1}/{total_edges}: 节点不存在 - {f} 或 {t}")
                continue

            # 检查边是否连通
            is_connected = self._is_edge_connected(edge)
            
            edge_info = {
                "from": f,
                "to": t,
                "from_name": self._get_node_display_name(f),
                "to_name": self._get_node_display_name(t),
                "edge_index": i,
                "devices": edge.get("devices", []),
                "devices_status": edge.get("devices_status", [])
            }

            if is_connected:
                self.graph[f].append(t)
                self.graph[t].append(f)
                self.connected_edges.append(edge_info)
            else:
                # 记录断开的边和导致断开的设备
                open_devices = []
                closed_devices = []
                
                for dev in edge.get("devices_status", []):
                    dev_name = dev.get("matched_name") or dev.get("name", "")
                    if dev.get("status") == "OPEN":
                        open_devices.append(dev_name)
                    else:
                        closed_devices.append(dev_name)
                
                edge_info.update({
                    "open_devices": open_devices,
                    "closed_devices": closed_devices
                })
                
                self.disconnected_edges.append(edge_info)

        # 记录统计信息
        connected_count = len(self.connected_edges)
        disconnected_count = len(self.disconnected_edges)
        
        self.logger.info(f"图构建完成 - 连通边: {connected_count}, 断开边: {disconnected_count}")
        
        # 记录断开边的详情（限制数量以避免日志过长）
        if disconnected_count > 0:
            self.logger.info("断开的边详情:")
            for i, edge in enumerate(self.disconnected_edges[:log_limit]):
                devices_str = ", ".join(edge["open_devices"])
                self.logger.info(f"  {edge['from_name']} <-> {edge['to_name']}: [{devices_str}]")
            
            if disconnected_count > log_limit:
                self.logger.info(f"  ... 还有 {disconnected_count - log_limit} 条断开的边")

    def _is_edge_connected(self, edge: Dict[str, Any]) -> bool:
        """
        判断边是否连通
        
        Args:
            edge: 边数据
            
        Returns:
            是否连通
        """
        if not self.consider_status:
            return True
        
        # 检查设备状态
        devices_status = edge.get("devices_status", [])
        if not devices_status:
            return True
        
        # 如果任何设备状态为OPEN，则边断开
        for dev in devices_status:
            if dev.get("status") == "OPEN":
                return False
        
        return True

    def _get_node_display_name(self, node_id: str) -> str:
        """
        获取节点的显示名称
        
        Args:
            node_id: 节点ID
            
        Returns:
            显示名称
        """
        node = self.nodes.get(node_id, {})
        return node.get("matched_name") or node.get("name") or node_id

    def find_islands(self):
        """使用BFS算法查找连通分量（岛屿）"""
        visited = set()
        self.islands = []
        total_nodes = len(self.nodes)
        
        self.logger.info(f"开始岛屿分析，总节点数: {total_nodes}")

        for node_id in self.nodes:
            if node_id not in visited:
                island = self._bfs_island(node_id, visited)
                if island:
                    self.islands.append(island)

        island_count = len(self.islands)
        self.logger.info(f"岛屿分析完成，共发现 {island_count} 个岛屿")
        
        # 统计岛屿规模分布
        self._log_island_distribution()

    def _bfs_island(self, start_node: str, visited: Set[str]) -> List[str]:
        """
        使用BFS查找从起始节点开始的连通分量
        
        Args:
            start_node: 起始节点
            visited: 已访问节点集合
            
        Returns:
            岛屿节点列表
        """
        if start_node in visited:
            return []

        island = []
        queue = deque([start_node])
        visited.add(start_node)

        while queue:
            current = queue.popleft()
            island.append(current)

            for neighbor in self.graph[current]:
                if neighbor not in visited:
                    visited.add(neighbor)
                    queue.append(neighbor)

        return island

    def _log_island_distribution(self):
        """记录岛屿规模分布"""
        size_distribution = defaultdict(int)
        
        for island in self.islands:
            size = len(island)
            size_distribution[size] += 1
        
        self.logger.info("岛屿规模分布:")
        # 按岛屿大小降序排列
        for size in sorted(size_distribution.keys(), reverse=True):
            count = size_distribution[size]
            self.logger.info(f"  {size} 个节点的岛屿: {count} 个")

    def get_island_map(self) -> Dict[str, int]:
        """
        获取节点到岛屿索引的映射
        
        Returns:
            节点ID到岛屿索引的字典
        """
        island_map = {}
        for i, island in enumerate(self.islands):
            for node_id in island:
                island_map[node_id] = i
        return island_map

    def get_island_info(self, island_index: int) -> Dict[str, Any]:
        """
        获取指定岛屿的详细信息
        
        Args:
            island_index: 岛屿索引
            
        Returns:
            岛屿信息字典
        """
        if island_index >= len(self.islands):
            return {}
        
        island_nodes = self.islands[island_index]
        max_samples = self.config.get("islands.max_sample_nodes", 3)
        
        # 统计电压分布
        voltage_distribution = defaultdict(int)
        type_distribution = defaultdict(int)
        
        for node_id in island_nodes:
            node = self.nodes.get(node_id, {})
            
            # 电压统计
            voltage = node.get("voltage", "未知")
            voltage_distribution[voltage] += 1
            
            # 类型统计
            node_type = node.get("type", "未知")
            type_distribution[node_type] += 1
        
        return {
            "index": island_index,
            "node_count": len(island_nodes),
            "nodes": island_nodes,
            "sample_nodes": [self._get_node_display_name(nid) for nid in island_nodes[:max_samples]],
            "voltage_distribution": dict(voltage_distribution),
            "type_distribution": dict(type_distribution)
        }


class IslandDetector(LoggerMixin):
    """岛屿检测器包装类"""
    
    def __init__(self, config: Optional[Config] = None):
        """
        初始化岛屿检测器
        
        Args:
            config: 配置对象
        """
        super().__init__()
        self.config = config or Config()
    
    def detect_islands(self, topology: Dict[str, Any]) -> Dict[str, Any]:
        """
        检测拓扑中的岛屿
        
        Args:
            topology: 拓扑数据
            
        Returns:
            岛屿检测结果
        """
        nodes = topology.get("nodes", [])
        edges = topology.get("edges", [])
        consider_status = self.config.get("islands.consider_status", True)
        
        # 创建岛屿检测实例
        grid_islands = GridIslands(nodes, edges, consider_status, self.config)
        
        # 查找岛屿
        grid_islands.find_islands()
        
        # 构建结果
        result = {
            "islands": grid_islands.islands,
            "island_map": grid_islands.get_island_map(),
            "connected_edges": grid_islands.connected_edges,
            "disconnected_edges": grid_islands.disconnected_edges,
            "statistics": {
                "total_nodes": len(nodes),
                "total_edges": len(edges),
                "island_count": len(grid_islands.islands),
                "connected_edges_count": len(grid_islands.connected_edges),
                "disconnected_edges_count": len(grid_islands.disconnected_edges)
            }
        }
        
        return result

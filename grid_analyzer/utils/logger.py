"""
日志管理模块
"""

import logging
import sys
from pathlib import Path
from typing import Optional

from .config import Config


def setup_logging(config: Optional[Config] = None) -> None:
    """
    设置全局日志配置
    
    Args:
        config: 配置对象，如果为None则使用默认配置
    """
    if config is None:
        config = Config()
    
    # 获取日志配置
    log_level = getattr(logging, config.get("logging.level", "INFO").upper())
    log_format = config.get("logging.format", "%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    console_output = config.get("logging.console_output", True)
    file_output = config.get("logging.file_output", True)
    log_dir = config.get("logging.log_dir", "out")
    log_filename = config.get("logging.log_filename", "grid_analysis.log")
    file_encoding = config.get("logging.file_encoding", "utf-8")
    
    # 清除现有的处理器
    root_logger = logging.getLogger()
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 设置根日志级别
    root_logger.setLevel(log_level)
    
    # 创建格式器
    formatter = logging.Formatter(log_format)
    
    handlers = []
    
    # 控制台处理器
    if console_output:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(log_level)
        console_handler.setFormatter(formatter)
        handlers.append(console_handler)
    
    # 文件处理器
    if file_output:
        log_path = Path(log_dir) / log_filename
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.FileHandler(log_path, encoding=file_encoding)
        file_handler.setLevel(log_level)
        file_handler.setFormatter(formatter)
        handlers.append(file_handler)
    
    # 添加处理器到根日志器
    for handler in handlers:
        root_logger.addHandler(handler)


def get_logger(name: str, config: Optional[Config] = None) -> logging.Logger:
    """
    获取日志器实例
    
    Args:
        name: 日志器名称，通常使用 __name__
        config: 配置对象
        
    Returns:
        配置好的日志器实例
    """
    # 如果还没有设置过全局日志配置，则设置一次
    root_logger = logging.getLogger()
    if not root_logger.handlers:
        setup_logging(config)
    
    return logging.getLogger(name)


class LoggerMixin:
    """日志混入类，为其他类提供日志功能"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._logger = None
    
    @property
    def logger(self) -> logging.Logger:
        """获取日志器实例"""
        if self._logger is None:
            self._logger = get_logger(self.__class__.__name__)
        return self._logger


# 为了兼容现有代码，提供一个简单的日志配置函数
def configure_logging(log_file: str = "out/grid_analysis.log", 
                     level: str = "INFO",
                     console: bool = True) -> logging.Logger:
    """
    简单的日志配置函数，用于向后兼容
    
    Args:
        log_file: 日志文件路径
        level: 日志级别
        console: 是否输出到控制台
        
    Returns:
        配置好的日志器
    """
    # 创建临时配置
    temp_config = Config()
    temp_config.set("logging.level", level)
    temp_config.set("logging.file_output", True)
    temp_config.set("logging.console_output", console)
    
    # 从文件路径中提取目录和文件名
    log_path = Path(log_file)
    temp_config.set("logging.log_dir", str(log_path.parent))
    temp_config.set("logging.log_filename", log_path.name)
    
    # 设置日志
    setup_logging(temp_config)
    
    return get_logger(__name__)

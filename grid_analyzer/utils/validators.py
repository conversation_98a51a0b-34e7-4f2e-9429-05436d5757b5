"""
数据验证模块
"""

from typing import Any, Dict, List, Optional


class ValidationError(Exception):
    """验证错误异常"""
    pass


def validate_topology_data(topology: Dict[str, Any], strict: bool = False) -> None:
    """
    验证拓扑数据格式
    
    Args:
        topology: 拓扑数据字典
        strict: 是否启用严格模式
        
    Raises:
        ValidationError: 数据格式错误
    """
    if not isinstance(topology, dict):
        raise ValidationError("Topology data must be a dictionary")
    
    # 检查必需的字段
    required_fields = ["nodes", "edges"]
    for field in required_fields:
        if field not in topology:
            raise ValidationError(f"Missing required field: {field}")
    
    # 验证节点数据
    nodes = topology["nodes"]
    if not isinstance(nodes, list):
        raise ValidationError("Nodes must be a list")
    
    node_ids = set()
    for i, node in enumerate(nodes):
        if not isinstance(node, dict):
            raise ValidationError(f"Node {i} must be a dictionary")
        
        if "id" not in node:
            raise ValidationError(f"Node {i} missing required field: id")
        
        node_id = node["id"]
        if node_id in node_ids:
            raise ValidationError(f"Duplicate node ID: {node_id}")
        node_ids.add(node_id)
        
        if strict:
            # 严格模式下检查更多字段
            if "name" not in node:
                raise ValidationError(f"Node {i} missing field: name")
    
    # 验证边数据
    edges = topology["edges"]
    if not isinstance(edges, list):
        raise ValidationError("Edges must be a list")
    
    for i, edge in enumerate(edges):
        if not isinstance(edge, dict):
            raise ValidationError(f"Edge {i} must be a dictionary")
        
        required_edge_fields = ["from", "to"]
        for field in required_edge_fields:
            if field not in edge:
                raise ValidationError(f"Edge {i} missing required field: {field}")
        
        if strict:
            # 严格模式下检查节点是否存在
            from_node = edge["from"].split(" ")[0]  # 提取节点ID
            to_node = edge["to"].split(" ")[0]
            
            if from_node not in node_ids:
                raise ValidationError(f"Edge {i} references non-existent from node: {from_node}")
            
            if to_node not in node_ids:
                raise ValidationError(f"Edge {i} references non-existent to node: {to_node}")


def validate_maintenance_plan(maintenance_plan: List[Dict[str, str]]) -> None:
    """
    验证检修计划格式
    
    Args:
        maintenance_plan: 检修计划列表
        
    Raises:
        ValidationError: 检修计划格式错误
    """
    if not isinstance(maintenance_plan, list):
        raise ValidationError("Maintenance plan must be a list")
    
    for i, device in enumerate(maintenance_plan):
        if not isinstance(device, dict):
            raise ValidationError(f"Maintenance device {i} must be a dictionary")
        
        if "name" not in device:
            raise ValidationError(f"Maintenance device {i} missing required field: name")
        
        if not isinstance(device["name"], str) or not device["name"].strip():
            raise ValidationError(f"Maintenance device {i} name must be a non-empty string")


def validate_island_data(island_data: Dict[str, Any]) -> None:
    """
    验证岛屿数据格式
    
    Args:
        island_data: 岛屿数据字典
        
    Raises:
        ValidationError: 岛屿数据格式错误
    """
    if not isinstance(island_data, dict):
        raise ValidationError("Island data must be a dictionary")
    
    required_fields = ["islands", "island_map"]
    for field in required_fields:
        if field not in island_data:
            raise ValidationError(f"Missing required field: {field}")
    
    islands = island_data["islands"]
    if not isinstance(islands, list):
        raise ValidationError("Islands must be a list")
    
    island_map = island_data["island_map"]
    if not isinstance(island_map, dict):
        raise ValidationError("Island map must be a dictionary")


def validate_config(config: Dict[str, Any]) -> None:
    """
    验证配置数据格式
    
    Args:
        config: 配置数据字典
        
    Raises:
        ValidationError: 配置格式错误
    """
    if not isinstance(config, dict):
        raise ValidationError("Config must be a dictionary")
    
    # 验证日志配置
    if "logging" in config:
        logging_config = config["logging"]
        if not isinstance(logging_config, dict):
            raise ValidationError("Logging config must be a dictionary")
        
        if "level" in logging_config:
            valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
            if logging_config["level"] not in valid_levels:
                raise ValidationError(f"Invalid logging level: {logging_config['level']}")


def validate_file_path(file_path: str, must_exist: bool = True) -> None:
    """
    验证文件路径
    
    Args:
        file_path: 文件路径
        must_exist: 文件是否必须存在
        
    Raises:
        ValidationError: 文件路径错误
    """
    if not isinstance(file_path, str) or not file_path.strip():
        raise ValidationError("File path must be a non-empty string")
    
    if must_exist:
        from pathlib import Path
        if not Path(file_path).exists():
            raise ValidationError(f"File does not exist: {file_path}")


def validate_output_format(format_name: str) -> None:
    """
    验证输出格式
    
    Args:
        format_name: 格式名称
        
    Raises:
        ValidationError: 格式不支持
    """
    valid_formats = ["json", "markdown", "html", "yaml"]
    if format_name.lower() not in valid_formats:
        raise ValidationError(f"Unsupported output format: {format_name}. "
                            f"Valid formats: {', '.join(valid_formats)}")


# 便捷的验证装饰器
def validate_input(validator_func):
    """
    输入验证装饰器
    
    Args:
        validator_func: 验证函数
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            # 这里可以根据需要实现具体的验证逻辑
            return func(*args, **kwargs)
        return wrapper
    return decorator

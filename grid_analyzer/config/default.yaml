# 电网拓扑分析系统默认配置

# 日志配置
logging:
  level: INFO
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_encoding: utf-8
  console_output: true
  file_output: true
  log_dir: "out"
  log_filename: "grid_analysis.log"

# 输出配置
output:
  default_dir: "out"
  encoding: utf-8
  json_indent: 2
  ensure_ascii: false

# 岛屿分析配置
islands:
  consider_status: true
  max_sample_nodes: 3
  log_disconnected_edges_limit: 10
  
# 检修分析配置
maintenance:
  validate_updates: true
  backup_original: true
  
# 报告生成配置
reports:
  default_format: "markdown"
  include_timestamp: true
  include_metadata: true
  
# 图分析配置
graph:
  use_bfs: true
  track_edge_details: true
  
# 数据验证配置
validation:
  strict_mode: false
  check_node_existence: true
  check_edge_consistency: true

# 性能配置
performance:
  batch_size: 1000
  memory_limit_mb: 1024
  enable_progress_bar: true

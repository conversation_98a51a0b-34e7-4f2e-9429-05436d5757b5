#!/usr/bin/env python3
"""
电网拓扑分析系统安装脚本
"""

from setuptools import setup, find_packages
import os

# 读取README文件
def read_readme():
    readme_path = os.path.join(os.path.dirname(__file__), 'README.md')
    if os.path.exists(readme_path):
        with open(readme_path, 'r', encoding='utf-8') as f:
            return f.read()
    return "电网拓扑分析系统"

# 读取requirements.txt
def read_requirements():
    req_path = os.path.join(os.path.dirname(__file__), 'requirements.txt')
    requirements = []
    if os.path.exists(req_path):
        with open(req_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    requirements.append(line)
    return requirements

setup(
    name="grid-topology-analyzer",
    version="1.0.0",
    author="Grid Analysis Team",
    author_email="<EMAIL>",
    description="电网拓扑分析系统 - 用于电网检修影响分析和岛屿变化检测",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/your-org/grid-topology-analyzer",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Science/Research",
        "Topic :: Scientific/Engineering",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-cov>=4.0.0",
            "black>=22.0.0",
            "flake8>=4.0.0",
            "isort>=5.10.0",
            "mypy>=0.950",
        ],
        "docs": [
            "sphinx>=4.5.0",
            "sphinx-rtd-theme>=1.0.0",
        ],
        "viz": [
            "matplotlib>=3.5.0",
            "plotly>=5.0.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "grid-analyzer=grid_analyzer.cli:main",
            "grid-maintenance=grid_analyzer.maintenance.cli:main",
            "grid-islands=grid_analyzer.islands.cli:main",
        ],
    },
    include_package_data=True,
    package_data={
        "grid_analyzer": [
            "config/*.yaml",
            "templates/*.md",
        ],
    },
    zip_safe=False,
)
